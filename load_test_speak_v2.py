#!/usr/bin/env python3
"""
Load test script for /speak_v2 endpoint using Locust
Tests 100 concurrent requests to evaluate performance and load balancing
"""

import json
import time
import random
from locust import HttpUser, task, between, events
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SpeakV2LoadTest(HttpUser):
    wait_time = between(1, 3)  # Wait 1-3 seconds between requests
    
    def on_start(self):
        """Called when a user starts - login and get access token"""
        self.login()
    
    def login(self):
        """Login to get access token"""
        login_data = {
            "grant_type": "password",
            "username": "superadmin", 
            "password": "godmod",  # Default password from core/tenant.py
            "scope": "",
            "client_id": "voice"  # Correct tenant slug
        }
        
        # Login to get access token
        with self.client.post("/login", data=login_data, catch_response=True) as response:
            if response.status_code == 200:
                result = response.json()
                self.access_token = result["access_token"]
                logger.info(f"Login successful for user {result['username']}")
            else:
                logger.error(f"Login failed: {response.status_code} - {response.text}")
                response.failure(f"Login failed: {response.status_code}")
                self.access_token = None
    
    @task
    def test_speak_v2_endpoint(self):
        """Test the /speak_v2 endpoint with various parameters"""
        if not self.access_token:
            logger.error("No access token available, skipping request")
            return
        
        # Sample texts for testing
        test_texts = [
            "technology has transformed nearly every aspect of modern life, from the way we communicate to how we work and learn.",
            "artificial intelligence is revolutionizing industries and changing how we interact with technology.",
            "the future of voice synthesis looks promising with advances in neural networks and machine learning.",
            "load testing helps ensure our systems can handle high traffic and concurrent users effectively.",
            "voice cloning technology enables personalized audio experiences for users across different applications."
        ]
        
        # Available preset voices (from your example)
        preset_voices = ["formal", "guided", "learning", "news-anchor", "podcast-host", "promo"]
        
        # Test data
        test_data = {
            "text": random.choice(test_texts),
            "preset_voice": random.choice(preset_voices),
            "user_voice_id": "6846a63e8c246ef2182ea246"  # From your example
        }
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Accept": "application/json"
        }
        
        start_time = time.time()
        
        with self.client.post("/v2/speak_v2", 
                            data=test_data, 
                            headers=headers, 
                            catch_response=True,
                            timeout=300) as response:  # 5 minute timeout for audio processing
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    # Log successful response details
                    logger.info(f"✅ Request successful in {response_time:.2f}s")
                    logger.info(f"   Text length: {len(test_data['text'])} chars")
                    logger.info(f"   Preset voice: {test_data['preset_voice']}")
                    logger.info(f"   Final file size: {result.get('final_file_size', 'N/A')} bytes")
                    
                    # Check workflow steps
                    if 'workflow_steps' in result:
                        steps = result['workflow_steps']
                        logger.info(f"   TTS size: {steps.get('voice_generation_tts_size', 'N/A')} bytes")
                        logger.info(f"   Timbre transfer size: {steps.get('timbre_transfer_size', 'N/A')} bytes")
                    
                    response.success()
                    
                except json.JSONDecodeError:
                    logger.error("❌ Invalid JSON response")
                    response.failure("Invalid JSON response")
            else:
                logger.error(f"❌ Request failed: {response.status_code} - {response.text}")
                response.failure(f"HTTP {response.status_code}")

# Custom event handlers for detailed reporting
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    if exception:
        logger.error(f"Request failed: {name} - {exception}")

@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    logger.info("🚀 Starting load test for /speak_v2 endpoint")
    logger.info(f"Target host: {environment.host}")
    logger.info("Test configuration:")
    logger.info("  - Endpoint: /v2/speak_v2")
    logger.info("  - Authentication: Bearer token")
    logger.info("  - Timeout: 300 seconds")
    logger.info("  - Concurrent users: 100")

@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    logger.info("🏁 Load test completed")
    
    # Print summary statistics
    stats = environment.stats
    logger.info("📊 Test Results Summary:")
    logger.info(f"  Total requests: {stats.total.num_requests}")
    logger.info(f"  Failed requests: {stats.total.num_failures}")
    logger.info(f"  Success rate: {((stats.total.num_requests - stats.total.num_failures) / stats.total.num_requests * 100):.2f}%")
    logger.info(f"  Average response time: {stats.total.avg_response_time:.2f}ms")
    logger.info(f"  Min response time: {stats.total.min_response_time:.2f}ms")
    logger.info(f"  Max response time: {stats.total.max_response_time:.2f}ms")
    logger.info(f"  Requests per second: {stats.total.current_rps:.2f}")

if __name__ == "__main__":
    # Run the load test directly
    import subprocess
    import sys
    
    print("🔧 Installing Locust if not already installed...")
    try:
        import locust
    except ImportError:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "locust"])
    
    print("🚀 Starting load test with 100 concurrent users...")
    print("📊 Web UI will be available at: http://localhost:8089")
    print("⚠️  Note: Adjust the tenant slug 'nextai' if different in your setup")
    
    # Run locust command
    cmd = [
        "locust", 
        "-f", __file__,
        "--host", "http://*************:8400",  # Your server IP
        "--users", "100",
        "--spawn-rate", "10",
        "--run-time", "5m",
        "--html", "load_test_report.html"
    ]
    
    subprocess.run(cmd)
