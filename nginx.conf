# nginx.conf for Docker - Load Balancing Timbre Transfer APIs
# This configuration implements round-robin load balancing between two timbre-transfer servers

# Main context
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

# Events context
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP context
http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'upstream_addr=$upstream_addr '
                    'upstream_response_time=$upstream_response_time '
                    'request_time=$request_time';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;  # Allow large audio file uploads
    client_body_buffer_size 10M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Upstream configuration for timbre-transfer load balancing
    upstream timbre_transfer_backend {
        # Round-robin load balancing (default method)
        # Requests will be distributed evenly between the two servers
        
        server *************:8011;  # First timbre-transfer server
        server *************:8011;  # Second timbre-transfer server
        
        # Optional: Add health checks and failover settings
        # keepalive 32;  # Keep connections alive for better performance
    }
    
    # Main server configuration
    server {
        listen 80;
        server_name localhost;
        
        # Health check endpoint for the load balancer itself
        location /nginx-health {
            access_log off;
            return 200 "nginx load balancer healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Load balancer status (optional - for monitoring)
        location /nginx-status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow **********/12;  # Allow internal Docker networks
            deny all;
        }
        
        # Timbre transfer API load balancing
        location /timbre-transfer/ {
            # Proxy to the upstream backend
            proxy_pass http://timbre_transfer_backend/timbre-transfer/;
            
            # Essential proxy headers
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Timeout settings for audio processing
            proxy_connect_timeout 30s;
            proxy_send_timeout 300s;      # 5 minutes for large uploads
            proxy_read_timeout 300s;      # 5 minutes for processing
            
            # Buffer settings for large audio files
            proxy_buffering on;
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;
            proxy_temp_file_write_size 256k;
            proxy_max_temp_file_size 1024m;
            
            # Request body settings for audio uploads
            client_body_buffer_size 10M;
            client_body_timeout 60s;
            
            # Add custom headers for debugging
            add_header X-Upstream-Server $upstream_addr always;
            add_header X-Response-Time $upstream_response_time always;
            
            # Handle errors gracefully
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 2;
            proxy_next_upstream_timeout 30s;
        }
        
        # Fallback location for other requests
        location / {
            return 404 "Endpoint not found. Use /timbre-transfer/ for API requests.";
            add_header Content-Type text/plain;
        }
        
        # Error pages
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
    
    # Optional: HTTPS server configuration (if SSL certificates are available)
    # server {
    #     listen 443 ssl http2;
    #     server_name localhost;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     # Same location blocks as HTTP server
    #     location /timbre-transfer/ {
    #         proxy_pass http://timbre_transfer_backend/timbre-transfer/;
    #         # ... same proxy settings as above
    #     }
    # }
}
