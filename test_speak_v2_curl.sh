#!/bin/bash

# Test script for /speak_v2 endpoint using curl
# Based on your successful response example

set -e  # Exit on any error

# Configuration
SERVER_URL="http://*************:8400"
TENANT_SLUG="voice"  # Correct tenant slug
USERNAME="superadmin"
PASSWORD="godmod"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Testing /speak_v2 endpoint${NC}"
echo -e "${BLUE}Server: ${SERVER_URL}${NC}"
echo -e "${BLUE}Tenant: ${TENANT_SLUG}${NC}"
echo ""

# Step 1: Login to get access token
echo -e "${YELLOW}📝 Step 1: Logging in...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "${SERVER_URL}/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=${USERNAME}&password=${PASSWORD}&scope=&client_id=${TENANT_SLUG}")

# Check if login was successful
if echo "$LOGIN_RESPONSE" | grep -q "access_token"; then
    ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.access_token')
    USER_INFO=$(echo "$LOGIN_RESPONSE" | jq -r '.username')
    echo -e "${GREEN}✅ Login successful for user: ${USER_INFO}${NC}"
else
    echo -e "${RED}❌ Login failed:${NC}"
    echo "$LOGIN_RESPONSE" | jq .
    exit 1
fi

# Step 2: Test /speak_v2 endpoint
echo -e "${YELLOW}🎤 Step 2: Testing /speak_v2 endpoint...${NC}"

# Test data based on your example
TEST_TEXT="technology has transformed nearly every aspect of modern life, from the way we communicate to how we work and learn. with just a smartphone, people can access information, connect with others across the globe, and complete tasks that once required physical presence."
PRESET_VOICE="formal"
USER_VOICE_ID="6846a63e8c246ef2182ea246"

echo -e "${BLUE}Request parameters:${NC}"
echo -e "  Text length: ${#TEST_TEXT} characters"
echo -e "  Preset voice: ${PRESET_VOICE}"
echo -e "  User voice ID: ${USER_VOICE_ID}"
echo ""

# Make the request with timing
echo -e "${YELLOW}⏱️  Making request (this may take 2-5 minutes for audio processing)...${NC}"
START_TIME=$(date +%s)

SPEAK_RESPONSE=$(curl -s -X POST "${SERVER_URL}/v2/speak_v2" \
  -H "Authorization: Bearer ${ACCESS_TOKEN}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "text=${TEST_TEXT}&preset_voice=${PRESET_VOICE}&user_voice_id=${USER_VOICE_ID}" \
  --max-time 300)  # 5 minute timeout

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

# Check response
if echo "$SPEAK_RESPONSE" | grep -q "final_audio_url"; then
    echo -e "${GREEN}✅ Request successful in ${DURATION} seconds${NC}"
    echo ""
    echo -e "${BLUE}📊 Response Summary:${NC}"
    
    # Parse and display key information
    MESSAGE=$(echo "$SPEAK_RESPONSE" | jq -r '.message')
    FINAL_SIZE=$(echo "$SPEAK_RESPONSE" | jq -r '.final_file_size')
    AUDIO_URL=$(echo "$SPEAK_RESPONSE" | jq -r '.final_audio_url')
    CREATED_AT=$(echo "$SPEAK_RESPONSE" | jq -r '.created_at')
    
    echo -e "  Message: ${MESSAGE}"
    echo -e "  Final file size: ${FINAL_SIZE} bytes"
    echo -e "  Created at: ${CREATED_AT}"
    echo -e "  Audio URL: ${AUDIO_URL:0:80}..."
    
    # Workflow steps
    if echo "$SPEAK_RESPONSE" | grep -q "workflow_steps"; then
        echo -e "${BLUE}🔄 Workflow Steps:${NC}"
        TTS_SIZE=$(echo "$SPEAK_RESPONSE" | jq -r '.workflow_steps.voice_generation_tts_size')
        CLONING_SIZE=$(echo "$SPEAK_RESPONSE" | jq -r '.workflow_steps.voice_cloning_size')
        TIMBRE_SIZE=$(echo "$SPEAK_RESPONSE" | jq -r '.workflow_steps.timbre_transfer_size')
        
        echo -e "  TTS Generation: ${TTS_SIZE} bytes"
        echo -e "  Voice Cloning: ${CLONING_SIZE} bytes"
        echo -e "  Timbre Transfer: ${TIMBRE_SIZE} bytes"
    fi
    
    echo ""
    echo -e "${GREEN}🎉 Test completed successfully!${NC}"
    
    # Save full response to file
    echo "$SPEAK_RESPONSE" | jq . > "speak_v2_test_response.json"
    echo -e "${BLUE}📄 Full response saved to: speak_v2_test_response.json${NC}"
    
else
    echo -e "${RED}❌ Request failed:${NC}"
    echo "$SPEAK_RESPONSE" | jq .
    exit 1
fi

echo ""
echo -e "${BLUE}🔗 Load Balancer Status:${NC}"
curl -s "http://*************:8011/nginx-status" || echo "Load balancer not accessible"

echo ""
echo -e "${BLUE}💡 To run load test with 100 concurrent users:${NC}"
echo -e "   python3 load_test_speak_v2.py"
