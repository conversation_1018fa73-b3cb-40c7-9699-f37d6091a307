# Docker Compose configuration for nginx load balancer
# Add this service to your existing docker-compose.yml file

services:
  # Nginx Load Balancer for Timbre Transfer APIs
  timbre-transfer-lb:
    image: nginx:1.25-alpine
    container_name: timbre-transfer-loadbalancer
    ports:
      - "8011:80"  # Map host port 8011 to nginx port 80
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    restart: unless-stopped
    networks:
      - voice_network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/nginx-health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "traefik.enable=false"  # If using Traefik
      - "description=Load balancer for timbre-transfer APIs"

  # Example timbre-transfer service configurations (if needed)
  # You can remove these if your services are running externally
  
  # timbre-transfer-1:
  #   image: your-timbre-transfer-image:latest
  #   container_name: timbre-transfer-1
  #   ports:
  #     - "8012:8011"  # Different host port to avoid conflicts
  #   networks:
  #     - voice_network
  #   restart: unless-stopped
  #   environment:
  #     - SERVICE_PORT=8011
  #     - SERVICE_NAME=timbre-transfer-1
  
  # timbre-transfer-2:
  #   image: your-timbre-transfer-image:latest
  #   container_name: timbre-transfer-2
  #   ports:
  #     - "8013:8011"  # Different host port to avoid conflicts
  #   networks:
  #     - voice_network
  #   restart: unless-stopped
  #   environment:
  #     - SERVICE_PORT=8011
  #     - SERVICE_NAME=timbre-transfer-2

volumes:
  nginx_logs:
    driver: local

networks:
  voice_network:
    driver: bridge
    external: false  # Creating a new network
