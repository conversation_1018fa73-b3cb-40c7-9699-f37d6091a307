# Nginx Load Balancer for Timbre Transfer APIs

This configuration implements round-robin load balancing between two timbre-transfer API servers using nginx in Docker.

## Files Created

1. **nginx.conf** - Complete nginx configuration with load balancing
2. **docker-compose-nginx.yml** - Docker Compose service configuration
3. **nginx-loadbalancer-README.md** - This documentation file

## Configuration Overview

### Upstream Backend
- **Server 1**: `*************:8011`
- **Server 2**: `*************:8011`
- **Load Balancing**: Round-robin (default nginx method)
- **Endpoint**: `/timbre-transfer/`

### Key Features

✅ **Round-robin load balancing** between two servers
✅ **Health checks** and automatic failover
✅ **Large file upload support** (100MB max)
✅ **Optimized timeouts** for audio processing (5 minutes)
✅ **Request/response logging** with upstream information
✅ **Error handling** with automatic retry
✅ **Performance optimizations** (gzip, keepalive, buffering)

## Setup Instructions

### Option 1: Add to Existing Docker Compose

1. Copy the nginx service from `docker-compose-nginx.yml` to your existing `docker-compose.yml`:

```yaml
services:
  # Your existing services...
  
  timbre-transfer-lb:
    image: nginx:1.25-alpine
    container_name: timbre-transfer-loadbalancer
    ports:
      - "8011:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    restart: unless-stopped
    networks:
      - voice_network
```

2. Update your application to use the load balancer:

```python
# Change from:
TIMBRE_TRANSFER_API_URL = "http://*************:8011/timbre-transfer/"

# To:
TIMBRE_TRANSFER_API_URL = "http://localhost:8011/timbre-transfer/"
# Or if running in Docker:
TIMBRE_TRANSFER_API_URL = "http://timbre-transfer-lb/timbre-transfer/"
```

### Option 2: Standalone Docker Compose

```bash
# Start the load balancer
docker-compose -f docker-compose-nginx.yml up -d

# Check status
docker-compose -f docker-compose-nginx.yml ps

# View logs
docker-compose -f docker-compose-nginx.yml logs -f timbre-transfer-lb
```

## Testing the Load Balancer

### Health Check
```bash
curl http://localhost:8011/nginx-health
# Expected: "nginx load balancer healthy"
```

### Load Balancer Status
```bash
curl http://localhost:8011/nginx-status
# Shows nginx statistics and active connections
```

### API Request Test
```bash
curl -X POST http://localhost:8011/timbre-transfer/ \
  -F "source_audio=@test.wav" \
  -F "reference_audio=@ref.wav" \
  -F "normalize_audio_flag=true"
```

## Monitoring

### Log Files
- **Access logs**: `/var/log/nginx/access.log`
- **Error logs**: `/var/log/nginx/error.log`

### Log Format Includes
- Upstream server address
- Response time from upstream
- Total request time
- Standard nginx access log fields

### Example Log Entry
```
************* - - [27/Jul/2024:10:30:45 +0000] "POST /timbre-transfer/ HTTP/1.1" *********** "-" "python-requests/2.28.1" "-" upstream_addr=*************:8011 upstream_response_time=15.234 request_time=15.456
```

## Load Balancing Behavior

### Round-Robin Distribution
- Request 1 → Server *************:8011
- Request 2 → Server *************:8011
- Request 3 → Server *************:8011
- Request 4 → Server *************:8011
- And so on...

### Failover Handling
- If one server fails, all requests go to the healthy server
- Failed servers are automatically retried
- Maximum 2 retry attempts per request
- 30-second timeout for upstream connections

## Configuration Customization

### Modify Load Balancing Method
Edit `nginx.conf` upstream block:

```nginx
upstream timbre_transfer_backend {
    # For weighted round-robin:
    server *************:8011 weight=3;
    server *************:8011 weight=1;
    
    # For least connections:
    least_conn;
    server *************:8011;
    server *************:8011;
    
    # For IP hash (session persistence):
    ip_hash;
    server *************:8011;
    server *************:8011;
}
```

### Adjust Timeouts
Modify timeout values in the location block:

```nginx
proxy_connect_timeout 30s;
proxy_send_timeout 600s;    # 10 minutes
proxy_read_timeout 600s;    # 10 minutes
```

## Troubleshooting

### Check Container Status
```bash
docker ps | grep timbre-transfer-loadbalancer
```

### View Real-time Logs
```bash
docker logs -f timbre-transfer-loadbalancer
```

### Test Individual Servers
```bash
# Test server 1 directly
curl http://*************:8011/timbre-transfer/

# Test server 2 directly  
curl http://*************:8011/timbre-transfer/
```

### Common Issues

1. **502 Bad Gateway**: Backend servers are down
2. **504 Gateway Timeout**: Processing takes longer than configured timeout
3. **413 Request Entity Too Large**: File size exceeds `client_max_body_size`

## Integration with Voice Processing App

Update your `voice_cloning_v2.py` configuration:

```python
# Replace the direct server URL with the load balancer
TIMBRE_TRANSFER_API_URL = "http://timbre-transfer-lb/timbre-transfer/"
# Or if accessing from outside Docker:
TIMBRE_TRANSFER_API_URL = "http://localhost:8011/timbre-transfer/"
```

This will automatically distribute timbre transfer requests between both servers, improving performance and reliability for concurrent requests.
