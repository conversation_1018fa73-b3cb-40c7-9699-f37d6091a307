pipeline {
    agent any

    environment {
        APP_DIR = "${env.WORKSPACE}"
    }

    stages {
        stage('Set Up .env File') {
            steps {
                dir("${APP_DIR}") {
                    withCredentials([file(credentialsId: 'env_vp', variable: 'ENV_FILE')]) {
                        sh '''
                            echo "📄 Copying .env file..."
                            cp "$ENV_FILE" .env
                            chmod 600 .env
                        '''
                    }
                }
            }
        }

        stage('Docker Build') {
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🏗 Building Docker images..."
                            sh 'docker compose --env-file .env build --no-cache'
                            env.BUILD_SUCCEEDED = "true"
                        } catch (Exception err) {
                            echo "❌ Build failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            slackSend channel: '#deployments', message: "❌ Build failed: ${env.JOB_NAME}"
                            env.BUILD_SUCCEEDED = "false"
                        }
                    }
                }
            }
        }

        stage('Deploy Main Services') {
            when {
                expression { env.BUILD_SUCCEEDED == "true" }
            }
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🚀 Rolling restart with new images..."
                            sh 'docker compose --env-file .env up -d --force-recreate --no-deps'

                            echo "✅ Main services deployment successful"
                        } catch (Exception err) {
                            echo "❌ Main services deploy failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            throw err
                        }
                    }
                }
            }
        }

        stage('Deploy Nginx Load Balancer') {
            when {
                expression { env.BUILD_SUCCEEDED == "true" }
            }
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🔄 Deploying nginx load balancer..."
                            sh 'docker compose -f docker-compose-nginx.yml up -d --force-recreate'

                            // Wait a moment for nginx to start
                            sleep(time: 5, unit: 'SECONDS')

                            // Health check
                            echo "🏥 Checking nginx health..."
                            sh 'curl -f http://localhost:8011/nginx-health || exit 1'

                            echo "✅ Nginx load balancer deployment successful"
                        } catch (Exception err) {
                            echo "❌ Nginx load balancer deploy failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            throw err
                        }
                    }
                }
            }
        }
    }

    post {
        success {
            echo '✅ Deployment completed successfully!'
            echo '📊 Services deployed:'
            echo '   - FastAPI application'
            echo '   - Nginx load balancer (port 8011)'
            echo '🔗 Load balancer health: http://*************:8011/nginx-health'
        }
        failure {
            echo '❌ Deployment failed!'
            echo '🔍 Check logs for details'
        }
        always {
            echo "📌 Deployment pipeline finished."
            // Cleanup only unused containers and images (NOT volumes)
            sh 'docker container prune -f || true'
            sh 'docker image prune -f || true'
        }
    }
}